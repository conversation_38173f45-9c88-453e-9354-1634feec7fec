import 'dotenv/config';
import type { DB } from '@/database';
import type { Session } from '@muraadso/types/sessions';
import type { User } from '@muraadso/types/users';
import type { RequestIdVariables } from 'hono/request-id';
import { z } from 'zod';

export const env = z
  .object({
    NODE_ENV: z.enum(['development', 'staging', 'production']),
    DATABASE_URL: z.string().nonempty(),
  })
  .parse(process.env);

export type Env = {
  Variables: RequestIdVariables & {
    db: DB;
    user?: User;
    session?: Session;
  };
  Bindings: {
    NODE_ENV: string;
    DATABASE_URL: string;
  };
};
