import { migrate } from 'drizzle-orm/postgres-js/migrator';
import { createDb } from './db';

require('dotenv').config({ path: '.dev.vars' });

(async () => {
  const url = process.env.DATABASE_URL!;
  console.log(url);
  const db = createDb(url);
  await migrate(db, {
    migrationsFolder: './src/database/migrations',
  });
  console.log('Migration done ✅!');
  process.exit(0);
})().catch((err) => {
  console.error(err);
  process.exit(1);
});
