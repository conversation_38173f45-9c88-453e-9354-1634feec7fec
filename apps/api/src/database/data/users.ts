import { faker } from '@faker-js/faker';
import { hashPassword } from '@/core/password';

export interface UserSeedData {
  email: string;
  phone: string;
  password: string;
  emailVerifiedAt: Date;
  phoneVerifiedAt: Date;
  isSysop: boolean;
  isBanned: boolean;
  lastSignInAt?: Date;
}

export interface UserGenerationOptions {
  sysopRatio?: number; // Percentage of users that should be sysops (0-1)
  verifiedRatio?: number; // Percentage of users that should be verified (0-1)
  bannedRatio?: number; // Percentage of users that should be banned (0-1)
  recentActivityRatio?: number; // Percentage of users with recent sign-in activity (0-1)
}

const DEFAULT_OPTIONS: Required<UserGenerationOptions> = {
  sysopRatio: 0.1, // 10% sysops
  verifiedRatio: 0.8, // 80% verified
  bannedRatio: 0.02, // 2% banned
  recentActivityRatio: 0.3, // 30% recent activity
};

/**
 * Generate realistic Somali/East African names and data
 */
const generateSomaliName = () => {
  const somaliFirstNames = [
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'
  ];
  
  con<PERSON> so<PERSON><PERSON><PERSON>astNames = [
    'Mohamed', 'Ahmed', 'Ali', 'Hassan', 'Omar', 'Abdi', 'Farah', 'Aden',
    'Yusuf', 'Abdullahi', 'Mohamud', 'Ibrahim', 'Ismail', 'Dahir', 'Nur',
    'Osman', 'Salad', 'Jama', 'Hersi', 'Warsame', 'Bile', 'Duale', 'Elmi'
  ];

  return {
    firstName: faker.helpers.arrayElement(somaliFirstNames),
    lastName: faker.helpers.arrayElement(somaliLastNames),
  };
};

/**
 * Generate a realistic Somali phone number
 */
const generateSomaliPhone = () => {
  // Somali mobile numbers typically start with +252 6/7
  const prefixes = ['61', '62', '63', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79'];
  const prefix = faker.helpers.arrayElement(prefixes);
  const number = faker.string.numeric(7);
  return `+252${prefix}${number}`;
};

/**
 * Generate a single user with realistic data
 */
export const generateUser = (options: UserGenerationOptions = {}): UserSeedData => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const { firstName, lastName } = generateSomaliName();
  
  const isSysop = faker.datatype.boolean({ probability: opts.sysopRatio });
  const isVerified = faker.datatype.boolean({ probability: opts.verifiedRatio });
  const isBanned = faker.datatype.boolean({ probability: opts.bannedRatio });
  const hasRecentActivity = faker.datatype.boolean({ probability: opts.recentActivityRatio });

  // Generate email based on name
  const emailDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'muraadso.com'];
  const domain = faker.helpers.arrayElement(emailDomains);
  const emailPrefix = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${faker.number.int({ min: 1, max: 999 })}`;
  const email = `${emailPrefix}@${domain}`;

  const phone = generateSomaliPhone();
  
  // Default password for all seeded users (should be changed in production)
  const password = hashPassword('password123');

  const now = new Date();
  const emailVerifiedAt = isVerified ? faker.date.past({ years: 1 }) : null;
  const phoneVerifiedAt = isVerified ? faker.date.past({ years: 1 }) : null;
  
  let lastSignInAt: Date | undefined;
  if (hasRecentActivity && isVerified && !isBanned) {
    lastSignInAt = faker.date.recent({ days: 30 });
  }

  return {
    email,
    phone,
    password,
    emailVerifiedAt: emailVerifiedAt || now,
    phoneVerifiedAt: phoneVerifiedAt || now,
    isSysop,
    isBanned,
    lastSignInAt,
  };
};

/**
 * Generate multiple users
 */
export const generateUsers = (count: number, options: UserGenerationOptions = {}): UserSeedData[] => {
  return Array.from({ length: count }, () => generateUser(options));
};

/**
 * Generate users with specific roles
 */
export const generateSysopUsers = (count: number): UserSeedData[] => {
  return generateUsers(count, { sysopRatio: 1, verifiedRatio: 1, bannedRatio: 0 });
};

export const generateCustomerUsers = (count: number): UserSeedData[] => {
  return generateUsers(count, { sysopRatio: 0, verifiedRatio: 0.9, bannedRatio: 0.01 });
};
