import { faker } from '@faker-js/faker';

export interface CustomerSeedData {
  userId?: string; // Optional - customers can exist without user accounts
  name: string;
  phone: string;
  email?: string;
  nationalId?: string;
  image?: string;
}

export interface CustomerGenerationOptions {
  linkedUserRatio?: number; // Percentage of customers linked to user accounts (0-1)
  emailRatio?: number; // Percentage of customers with email addresses (0-1)
  nationalIdRatio?: number; // Percentage of customers with national IDs (0-1)
  includeImages?: boolean;
  imageProvider?: 'placeholder' | 'avatar' | 'none';
}

const DEFAULT_OPTIONS: Required<CustomerGenerationOptions> = {
  linkedUserRatio: 0.4, // 40% linked to user accounts
  emailRatio: 0.6, // 60% have email addresses
  nationalIdRatio: 0.7, // 70% have national IDs
  includeImages: true,
  imageProvider: 'avatar',
};

/**
 * Generate realistic Somali/East African names for customers
 */
const generateCustomerName = () => {
  const somaliFirstNames = [
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
    '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Ikram', 'Sumaya', 'Warsan'
  ];

  const somaliLastNames = [
    'Mohamed', 'Ahmed', 'Ali', 'Hassan', 'Omar', 'Abdi', 'Farah', 'Aden',
    'Yusuf', 'Abdullahi', 'Mohamud', 'Ibrahim', 'Ismail', 'Dahir', 'Nur',
    'Osman', 'Salad', 'Jama', 'Hersi', 'Warsame', 'Bile', 'Duale', 'Elmi',
    'Shire', 'Guled', 'Diriye', 'Maxamed', 'Cabdi', 'Cali', 'Cumar'
  ];

  const firstName = faker.helpers.arrayElement(somaliFirstNames);
  const lastName = faker.helpers.arrayElement(somaliLastNames);

  return `${firstName} ${lastName}`;
};

/**
 * Generate a realistic Somali phone number
 */
const generateSomaliPhone = () => {
  // Somali mobile numbers typically start with +252 6/7
  const prefixes = ['61', '62', '63', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79'];
  const prefix = faker.helpers.arrayElement(prefixes);
  const number = faker.string.numeric(7);
  return `+252${prefix}${number}`;
};

/**
 * Generate a Somali national ID
 */
const generateSomaliNationalId = () => {
  // Somali national IDs are typically 10-digit numbers
  return faker.string.numeric(10);
};

/**
 * Generate profile image URL
 */
const generateProfileImage = (provider: 'placeholder' | 'avatar' | 'none', name: string): string | undefined => {
  switch (provider) {
    case 'placeholder':
      return `https://via.placeholder.com/150x150/28a745/ffffff?text=${encodeURIComponent(name.split(' ').map(n => n[0]).join(''))}`;
    case 'avatar':
      // Using DiceBear API for consistent avatar generation
      const seed = name.toLowerCase().replace(/\s+/g, '');
      return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&backgroundColor=28a745&textColor=ffffff`;
    case 'none':
    default:
      return undefined;
  }
};

/**
 * Generate a single customer
 */
export const generateCustomer = (
  availableUserIds: string[] = [],
  options: CustomerGenerationOptions = {}
): CustomerSeedData => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const name = generateCustomerName();

  // Determine if this customer should be linked to a user account
  const shouldLinkUser = faker.datatype.boolean({ probability: opts.linkedUserRatio });
  const userId = shouldLinkUser && availableUserIds.length > 0
    ? faker.helpers.arrayElement(availableUserIds)
    : undefined;

  const phone = generateSomaliPhone();

  // Generate email if specified
  let email: string | undefined;
  if (faker.datatype.boolean({ probability: opts.emailRatio })) {
    const emailDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
    const domain = faker.helpers.arrayElement(emailDomains);
    const emailPrefix = name.toLowerCase().replace(/\s+/g, '.') + faker.number.int({ min: 1, max: 999 });
    email = `${emailPrefix}@${domain}`;
  }

  // Generate national ID if specified
  const nationalId = faker.datatype.boolean({ probability: opts.nationalIdRatio })
    ? generateSomaliNationalId()
    : undefined;

  // Generate profile image
  let image: string | undefined;
  if (opts.includeImages) {
    image = generateProfileImage(opts.imageProvider, name);
  }

  return {
    userId,
    name,
    phone,
    email,
    nationalId,
    image,
  };
};

/**
 * Generate multiple customers
 */
export const generateCustomers = (
  count: number,
  availableUserIds: string[] = [],
  options: CustomerGenerationOptions = {}
): CustomerSeedData[] => {
  const customers: CustomerSeedData[] = [];
  const usedUserIds = new Set<string>();

  for (let i = 0; i < count; i++) {
    // Filter out already used user IDs to avoid duplicates
    const availableIds = availableUserIds.filter(id => !usedUserIds.has(id));
    const customer = generateCustomer(availableIds, options);

    if (customer.userId) {
      usedUserIds.add(customer.userId);
    }

    customers.push(customer);
  }

  return customers;
};

/**
 * Generate customers with high linkage to user accounts (for testing user relationships)
 */
export const generateLinkedCustomers = (
  count: number,
  availableUserIds: string[]
): CustomerSeedData[] => {
  return generateCustomers(count, availableUserIds, {
    linkedUserRatio: 0.9, // 90% linked
    emailRatio: 0.95, // 95% have emails
    nationalIdRatio: 0.8, // 80% have national IDs
  });
};

/**
 * Generate customers without user accounts (walk-in customers)
 */
export const generateWalkInCustomers = (count: number): CustomerSeedData[] => {
  return generateCustomers(count, [], {
    linkedUserRatio: 0, // 0% linked
    emailRatio: 0.3, // 30% have emails
    nationalIdRatio: 0.5, // 50% have national IDs
  });
};
