import { faker } from '@faker-js/faker';

export interface SysopSeedData {
  userId: string;
  name: string;
  image?: string;
}

export interface SysopGenerationOptions {
  includeImages?: boolean;
  imageProvider?: 'placeholder' | 'avatar' | 'none';
}

const DEFAULT_OPTIONS: Required<SysopGenerationOptions> = {
  includeImages: true,
  imageProvider: 'avatar',
};

/**
 * Generate realistic Somali/East African names for sysops
 */
const generateSysopName = () => {
  const somaliFirstNames = [
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
    '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'
  ];

  const somaliLastNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Warsame', 'Bile', 'Duale', '<PERSON>i'
  ];

  const firstName = faker.helpers.arrayElement(somaliFirstNames);
  const lastName = faker.helpers.arrayElement(somaliLastNames);

  return `${firstName} ${lastName}`;
};

/**
 * Generate profile image URL
 */
const generateProfileImage = (provider: 'placeholder' | 'avatar' | 'none', name: string): string | undefined => {
  switch (provider) {
    case 'placeholder':
      return `https://via.placeholder.com/150x150/0066cc/ffffff?text=${encodeURIComponent(name.split(' ').map(n => n[0]).join(''))}`;
    case 'avatar':
      // Using DiceBear API for consistent avatar generation
      const seed = name.toLowerCase().replace(/\s+/g, '');
      return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&backgroundColor=0066cc&textColor=ffffff`;
    case 'none':
    default:
      return undefined;
  }
};

/**
 * Generate a single sysop profile
 */
export const generateSysop = (userId: string, options: SysopGenerationOptions = {}): SysopSeedData => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const name = generateSysopName();

  let image: string | undefined;
  if (opts.includeImages) {
    image = generateProfileImage(opts.imageProvider, name);
  }

  return {
    userId,
    name,
    image,
  };
};

/**
 * Generate multiple sysop profiles
 */
export const generateSysops = (userIds: string[], options: SysopGenerationOptions = {}): SysopSeedData[] => {
  return userIds.map(userId => generateSysop(userId, options));
};

/**
 * Generate sysops with predefined admin accounts
 */
export const generateAdminSysops = (userIds: string[]): SysopSeedData[] => {
  const adminNames = [
    'Ahmed Mohamed', // System Administrator
    'Fatima Hassan', // Operations Manager
    'Omar Ali',      // Technical Lead
    'Amina Yusuf',   // Customer Support Lead
    'Hassan Abdi',   // Security Administrator
  ];

  return userIds.slice(0, Math.min(userIds.length, adminNames.length)).map((userId, index) => ({
    userId,
    name: adminNames[index],
    image: generateProfileImage('avatar', adminNames[index]),
  }));
};
