# Muraadso API

## Setup

1. Copy the environment variables:
   ```bash
   cp .dev.vars.example .dev.vars
   ```

2. Fill in your database URL and other environment variables in `.dev.vars`:
   ```
   NODE_ENV=development
   DATABASE_URL=postgresql://username:password@localhost:5432/muraadso
   ```

## Database

### Migrations

Run database migrations:
```bash
pnpm db:migrate
```

### Seeding

Seed the database with sample data:
```bash
pnpm db:seed
```

Start the development server:
```bash
pnpm dev
```

The API will be available at `http://localhost:9921`

## Testing

Run tests:
```bash
pnpm test
```
