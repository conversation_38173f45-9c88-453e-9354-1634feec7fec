import { z } from 'zod/v4';

const customerSchema = z.object({
  id: z.uuid(),
  userId: z.uuid().nullable().optional(),
  name: z.string(),
  phone: z.string(),
  email: z.string().email().nullable().optional(),
  nationalId: z.string().nullable().optional(),
  image: z.string().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const selectCustomerSchema = customerSchema;
export const createCustomerSchema = customerSchema.partial({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export const updateCustomerSchema = customerSchema.partial();

export type Customer = z.infer<typeof selectCustomerSchema>;
export type CreateCustomer = z.infer<typeof createCustomerSchema>;
export type UpdateCustomer = z.infer<typeof updateCustomerSchema>;
