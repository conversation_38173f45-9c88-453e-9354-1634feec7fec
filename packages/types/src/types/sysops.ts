import { z } from 'zod/v4';

const sysopSchema = z.object({
  id: z.uuid(),
  userId: z.uuid(),
  name: z.string(),
  image: z.string().nullable().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const selectSysopSchema = sysopSchema;
export const createSysopSchema = sysopSchema.partial({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export const updateSysopSchema = sysopSchema.partial();

export type Sysop = z.infer<typeof selectSysopSchema>;
export type CreateSysop = z.infer<typeof createSysopSchema>;
export type UpdateSysop = z.infer<typeof updateSysopSchema>;
